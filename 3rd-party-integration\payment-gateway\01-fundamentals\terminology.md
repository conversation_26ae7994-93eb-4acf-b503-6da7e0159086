# Payment Gateway Terminology

## 📚 Essential Terms & Definitions

Master the language of payment processing to communicate effectively with stakeholders and understand documentation.

---

## 🏦 Core Payment Entities

### **Merchant**
- Business that accepts payments for goods/services
- Also called "seller" or "vendor"
- Must have merchant account to receive funds
- Subject to underwriting and compliance requirements

### **Acquirer (Acquiring Bank)**
- Financial institution that processes payments for merchants
- Provides merchant accounts
- Assumes liability for merchant transactions
- Examples: Chase Paymentech, First Data

### **Issuer (Issuing Bank)**
- Bank that issued the customer's payment card
- Authorizes or declines transactions
- Manages customer's account and credit limits
- Examples: Chase, Bank of America, Capital One

### **Payment Gateway**
- Technology that captures and transmits payment data
- Connects merchants to payment processing networks
- Provides APIs and integration tools
- Examples: Stripe, PayPal, Authorize.Net

### **Payment Processor**
- Routes transactions between gateways and card networks
- Handles technical aspects of payment processing
- May be integrated with gateway or separate service
- Examples: Stripe (integrated), First Data, Worldpay

---

## 💳 Transaction Lifecycle Terms

### **Authorization**
- Permission to charge a payment method
- Verifies card validity and available funds
- Creates temporary hold on customer's account
- Expires after 7-30 days if not captured

### **Capture**
- Process of actually charging the authorized amount
- Converts authorization hold to actual charge
- Can be partial (less than authorized amount)
- Must occur within authorization validity period

### **Settlement**
- Transfer of funds from customer's bank to merchant's account
- Usually occurs 1-3 business days after capture
- Involves clearing through card networks
- Subject to processing fees and chargebacks

### **Void**
- Cancellation of an authorized but uncaptured transaction
- Removes hold from customer's account immediately
- Can only be done before settlement
- No fees typically charged for voids

### **Refund**
- Return of captured funds to customer
- Can be full or partial amount
- Takes 3-10 business days to appear on customer statement
- Subject to refund fees from payment processor

---

## 🔐 Security & Compliance

### **PCI DSS (Payment Card Industry Data Security Standard)**
- Security standards for organizations handling card data
- Four compliance levels based on transaction volume
- Requires secure networks, data protection, access controls
- Mandatory for all merchants accepting card payments

### **Tokenization**
- Replacement of sensitive card data with non-sensitive tokens
- Tokens are meaningless outside the tokenization system
- Reduces PCI compliance scope for merchants
- Enables secure storage of payment methods

### **3D Secure (3DS)**
- Additional authentication layer for online card payments
- Redirects customer to bank's authentication page
- Reduces fraud and shifts liability to issuing bank
- Version 2.0 provides better mobile experience

### **CVV (Card Verification Value)**
- 3-4 digit security code on payment cards
- Used to verify card is physically present
- Cannot be stored by merchants after authorization
- Also called CVC, CID, or security code

### **Address Verification Service (AVS)**
- Compares billing address with bank records
- Helps detect fraudulent transactions
- Returns match/no-match codes for address components
- Primarily used in US and Canada

---

## 💰 Fees & Pricing

### **Interchange**
- Fee paid by acquiring bank to issuing bank
- Set by card networks (Visa, Mastercard)
- Varies by card type, transaction method, merchant category
- Largest component of payment processing costs

### **Assessment Fees**
- Fees charged by card networks (Visa, Mastercard)
- Paid by acquirers for network access and services
- Typically small percentage of transaction volume
- Passed through to merchants in processing fees

### **Processing Fees**
- Fees charged by payment gateway/processor
- Usually percentage + fixed amount per transaction
- May include monthly fees, setup fees, chargeback fees
- Examples: 2.9% + $0.30 per transaction (Stripe)

### **Chargeback Fees**
- Fee charged when customer disputes transaction
- Typically $15-25 per chargeback regardless of outcome
- Covers administrative costs of dispute process
- Can increase for merchants with high chargeback rates

---

## 🔄 Payment Methods

### **Card Present (CP)**
- Physical card used at point-of-sale terminal
- Lower fraud risk, lower interchange rates
- Includes chip (EMV), contactless, magnetic stripe
- Requires card reader hardware

### **Card Not Present (CNP)**
- Online, phone, or mail order transactions
- Higher fraud risk, higher interchange rates
- Requires additional verification methods
- Subject to 3D Secure requirements in some regions

### **ACH (Automated Clearing House)**
- Electronic bank-to-bank transfers
- Lower fees than card payments
- Longer settlement times (3-5 business days)
- Popular for recurring payments and large amounts

### **Digital Wallets**
- Stored payment methods in mobile apps
- Examples: Apple Pay, Google Pay, Samsung Pay
- Use tokenization for security
- Often provide faster checkout experience

---

## 🏪 Business Models

### **Direct Merchant**
- Single business accepting payments
- Direct relationship with payment gateway
- Receives all funds minus processing fees
- Responsible for own compliance and risk management

### **Marketplace**
- Platform connecting multiple sellers with buyers
- Facilitates payments between parties
- Takes commission or transaction fees
- Examples: Amazon, Etsy, Uber, your healthcare platform

### **Payment Facilitator (PayFac)**
- Master merchant that enables sub-merchants
- Handles underwriting and compliance for sub-merchants
- Faster onboarding than traditional merchant accounts
- Examples: Stripe, Square, PayPal

### **Independent Sales Organization (ISO)**
- Third-party that sells payment processing services
- Partners with acquiring banks and processors
- Provides sales and support to merchants
- Traditional model for payment processing sales

---

## 📊 Transaction States

### **Pending**
- Transaction submitted but not yet processed
- Awaiting authorization from issuing bank
- May require additional authentication
- Customer should not be charged yet

### **Authorized**
- Payment method verified and funds reserved
- Hold placed on customer's account
- Ready for capture when goods/services delivered
- Will expire if not captured within time limit

### **Captured**
- Funds actually charged to customer's account
- Settlement process initiated
- Cannot be voided, only refunded
- Merchant will receive funds after settlement

### **Failed**
- Transaction could not be completed
- Reasons: declined card, network error, invalid data
- No charge to customer
- May be retryable depending on failure reason

### **Disputed**
- Customer has initiated chargeback process
- Funds may be temporarily withheld
- Merchant can provide evidence to contest
- Final resolution determined by card network

---

## 🌍 Regional Terms

### **SEPA (Single Euro Payments Area)**
- European bank transfer system
- Enables euro transfers across EU countries
- Lower cost alternative to card payments
- Popular for recurring payments in Europe

### **Open Banking**
- Regulation requiring banks to share customer data
- Enables account-to-account payments
- Growing in UK, EU, and other regions
- Reduces reliance on card networks

### **UPI (Unified Payments Interface)**
- Real-time payment system in India
- Enables instant bank-to-bank transfers
- Very low cost, high adoption
- Mobile-first payment experience

---

## 🏥 Healthcare-Specific Terms

### **HIPAA Compliance**
- US regulation for healthcare data protection
- Applies to payment data containing health information
- Requires additional security measures
- Important for your healthcare marketplace

### **Medical Billing Codes**
- Standardized codes for medical services
- May be required in payment metadata
- Examples: CPT codes, ICD-10 codes
- Useful for reporting and compliance

### **Telemedicine Payments**
- Payments for remote medical consultations
- May have different regulatory requirements
- Growing market segment post-COVID
- Your platform's primary use case

---

## 🔍 Common Abbreviations

| Term | Full Name | Description |
|------|-----------|-------------|
| **API** | Application Programming Interface | Software interface for integration |
| **BIN** | Bank Identification Number | First 6 digits of card number |
| **EMV** | Europay, Mastercard, Visa | Chip card standard |
| **KYC** | Know Your Customer | Identity verification requirements |
| **AML** | Anti-Money Laundering | Financial crime prevention |
| **SCA** | Strong Customer Authentication | EU requirement for payment authentication |
| **PSD2** | Payment Services Directive 2 | EU regulation for payment services |
| **SDK** | Software Development Kit | Pre-built code libraries |
| **TLS** | Transport Layer Security | Encryption protocol for data transmission |
| **JSON** | JavaScript Object Notation | Data format for API communication |

---

**Next**: [Gateway Comparison →](./comparison.md)
